2025-05-26 17:54:36,986 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-26 17:54:36,987 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 17:54:36,988 - werkzeug - INFO -  * Restarting with stat
2025-05-26 17:54:37,710 - werkzeug - WARNING -  * Debugger is active!
2025-05-26 17:54:37,715 - werkzeug - INFO -  * Debugger PIN: 330-528-438
2025-05-26 17:55:52,949 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-26 17:55:52,952 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 17:55:52,954 - werkzeug - INFO -  * Restarting with stat
2025-05-26 17:55:53,639 - werkzeug - WARNING -  * Debugger is active!
2025-05-26 17:55:53,642 - werkzeug - INFO -  * Debugger PIN: 330-528-438
2025-05-26 17:56:20,983 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-26 17:56:20,984 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 17:56:21,138 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-26 17:56:21,138 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 17:56:21,364 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-26 17:56:21,364 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 17:56:21,583 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-26 17:56:21,583 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 17:56:21,803 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-26 17:56:21,803 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 17:56:33,234 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 17:56:33] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-26 17:56:33,614 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 17:56:33] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-26 17:56:42,403 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 17:56:42] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-26 17:56:43,976 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 17:56:43] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-26 17:59:07,398 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 17:59:07] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-26 17:59:12,312 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 17:59:12] "[31m[1mGET /api/auth/login HTTP/1.1[0m" 405 -
2025-05-26 17:59:12,617 - auth_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /favicon.ico
2025-05-26 17:59:12,618 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 17:59:12] "[31m[1mGET /favicon.ico HTTP/1.1[0m" 401 -
2025-05-26 18:03:31,413 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-26 18:03:31,414 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 18:03:31,415 - werkzeug - INFO -  * Restarting with stat
2025-05-26 18:03:32,234 - werkzeug - WARNING -  * Debugger is active!
2025-05-26 18:03:32,237 - werkzeug - INFO -  * Debugger PIN: 330-528-438
2025-05-26 18:04:58,511 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:58] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-26 18:04:58,872 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:58] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-26 18:04:58,872 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:58] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-26 18:04:58,873 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:58] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-26 18:04:58,873 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:58] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-26 18:04:58,873 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:58] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-26 18:04:58,873 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:58] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-26 18:04:58,873 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:58] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-26 18:04:58,874 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:58] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-26 18:04:59,202 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:59] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-26 18:04:59,212 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:59] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-26 18:04:59,215 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:59] "GET /api/students/students HTTP/1.1" 200 -
2025-05-26 18:04:59,262 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:59] "GET /api/users/users HTTP/1.1" 200 -
2025-05-26 18:04:59,445 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:59] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-26 18:04:59,446 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:59] "GET /api/students/students HTTP/1.1" 200 -
2025-05-26 18:04:59,446 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:59] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-26 18:04:59,446 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:04:59] "GET /api/users/users HTTP/1.1" 200 -
2025-05-26 18:05:18,338 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:05:18] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-26 18:05:18,675 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:05:18] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-26 18:05:18,675 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:05:18] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-26 18:05:18,676 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:05:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-26 18:05:18,919 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:05:18] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-26 18:05:18,919 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:05:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-26 18:05:18,919 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:05:18] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-26 18:09:13,672 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:09:13] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-26 18:09:13,996 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:09:13] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-26 18:09:13,998 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:09:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-26 18:09:14,003 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:09:14] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-26 18:09:14,256 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:09:14] "GET /api/students/students HTTP/1.1" 200 -
2025-05-26 18:09:14,256 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:09:14] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-26 18:09:25,362 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:09:25] "GET /api/students/students HTTP/1.1" 200 -
2025-05-26 18:09:28,953 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:09:28] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-26 18:09:29,257 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:09:29] "GET /api/students/students HTTP/1.1" 200 -
2025-05-26 18:09:31,940 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:09:31] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-26 18:09:31,941 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:09:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-26 18:09:53,846 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:09:53] "OPTIONS /api/auth/forget-password HTTP/1.1" 200 -
2025-05-26 18:09:56,960 - auth_service.common.utils - INFO - OTP email sent <NAME_EMAIL>
2025-05-26 18:09:56,961 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:09:56] "POST /api/auth/forget-password HTTP/1.1" 200 -
2025-05-26 18:10:20,015 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:10:20] "OPTIONS /api/auth/verify-otp HTTP/1.1" 200 -
2025-05-26 18:10:20,335 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:10:20] "POST /api/auth/verify-otp HTTP/1.1" 200 -
2025-05-26 18:10:49,597 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:10:49] "OPTIONS /api/auth/reset-password HTTP/1.1" 200 -
2025-05-26 18:10:52,519 - auth_service.common.utils - INFO - Password reset confirmation email sent <NAME_EMAIL>
2025-05-26 18:10:52,520 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:10:52] "POST /api/auth/reset-password HTTP/1.1" 200 -
2025-05-26 18:11:01,782 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-26 18:11:01,783 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:11:01] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-26 18:11:14,432 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:11:14] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-26 18:11:14,794 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:11:14] "OPTIONS /api/students/student-profile HTTP/1.1" 200 -
2025-05-26 18:11:14,794 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:11:14] "OPTIONS /api/students/student-profile HTTP/1.1" 200 -
2025-05-26 18:11:15,113 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:11:15] "GET /api/students/student-profile HTTP/1.1" 200 -
2025-05-26 18:11:15,371 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:11:15] "GET /api/students/student-profile HTTP/1.1" 200 -
2025-05-26 18:11:15,433 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:11:15] "OPTIONS /api/courses/student-courses/5 HTTP/1.1" 200 -
2025-05-26 18:11:15,681 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:11:15] "OPTIONS /api/courses/student-courses/5 HTTP/1.1" 200 -
2025-05-26 18:11:15,760 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:11:15] "GET /api/courses/student-courses/5 HTTP/1.1" 200 -
2025-05-26 18:11:16,013 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:11:16] "GET /api/courses/student-courses/5 HTTP/1.1" 200 -
2025-05-26 18:12:21,387 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:12:21] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-26 18:12:21,710 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:12:21] "GET /api/students/student-profile HTTP/1.1" 200 -
2025-05-26 18:12:21,977 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:12:21] "GET /api/students/student-profile HTTP/1.1" 200 -
2025-05-26 18:12:22,024 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:12:22] "GET /api/courses/student-courses/5 HTTP/1.1" 200 -
2025-05-26 18:12:22,290 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 18:12:22] "GET /api/courses/student-courses/5 HTTP/1.1" 200 -
